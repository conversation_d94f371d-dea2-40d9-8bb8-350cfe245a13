.PHONY: build run


# 手动指定版本号构建
# 使用方式: make build VERSION=1.2.3
build:
	@if [ -z "$(VERSION)" ]; then \
		echo "Error: VERSION is required. Usage: make build VERSION=1.2.3"; \
		exit 1; \
	fi
	@echo "Building version: $(VERSION)"
	docker build -t zjl-server:$(VERSION) . && \
	docker tag zjl-server:$(VERSION) registry.rcztcs.com/zjl/zjl-server:$(VERSION) && \
	echo Docker image build success: zjl-server:$(VERSION)
	# && \
#	docker push registry.rcztcs.com/zjl/zjl-server:$(VERSION) && \
#	echo Docker image pushed: registry.rcztcs.com/zjl/zjl-server:$(VERSION)

# 运行容器 - 使用环境变量 ZJL_VERSION 指定版本，默认为1
# 示例: make run 或 ZJL_VERSION=2 make run
run:
	@echo "Creating network if not exists..."
	@docker network create zjl-network 2>/dev/null || true
	@echo "Starting zjl-server container..."
	docker run -d \
		--name zjl-app \
		--network zjl-network \
		-p 8080:8080 \
		-v ./uploads:/app/uploads \
		-v ./config:/app/config \
		-e TZ=Asia/Shanghai \
		--restart unless-stopped \
		registry.rcztcs.com/zjl/zjl-server:$(if $(ZJL_VERSION),$(ZJL_VERSION),1)
	@echo "Container zjl-app started successfully"
