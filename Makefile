.PHONY: version build run stop clean

v ?= $(shell scripts\version.bat get)

# 获取当前版本号
version:
	@echo version:
	@scripts\version.bat get

# 自动递增版本号构建
build:
	$(eval NEW_VERSION := $(shell scripts\version.bat next))
	@echo next version: $(NEW_VERSION)
	docker build -t zjl-server:$(NEW_VERSION) . && \
	docker tag zjl-server:$(NEW_VERSION) registry.rcztcs.com/zjl/zjl-server:$(NEW_VERSION) && \
	echo Docker image build success: zjl-server:$(NEW_VERSION) && \
	docker push registry.rcztcs.com/zjl/zjl-server:$(NEW_VERSION) && \
	echo Docker image pushed: registry.rcztcs.com/zjl/zjl-server:$(NEW_VERSION)

# 运行容器 - 使用环境变量 ZJL_VERSION 指定版本，默认为1
# 示例: make run 或 ZJL_VERSION=2 make run
run:
	@echo "Creating network if not exists..."
	@docker network create zjl-network 2>/dev/null || true
	@echo "Starting zjl-server container..."
	docker run -d \
		--name zjl-app \
		--network zjl-network \
		-p 8080:8080 \
		-v ./uploads:/app/uploads \
		-v ./config:/app/config \
		-v ./logs:/app/logs \
		-e TZ=Asia/Shanghai \
		--restart unless-stopped \
		registry.rcztcs.com/zjl/zjl-server:$(if $(ZJL_VERSION),$(ZJL_VERSION),1)
	@echo "Container zjl-app started successfully"

# 停止并删除容器
stop:
	@echo "Stopping zjl-app container..."
	@docker stop zjl-app 2>/dev/null || true
	@docker rm zjl-app 2>/dev/null || true
	@echo "Container zjl-app stopped and removed"

# 清理：停止容器、删除网络
clean: stop
	@echo "Removing network..."
	@docker network rm zjl-network 2>/dev/null || true
	@echo "Cleanup completed"